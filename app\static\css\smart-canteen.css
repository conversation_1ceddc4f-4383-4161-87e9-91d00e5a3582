/* 智慧食堂平台样式 */

/* 基础变量定义 */
:root {
  --primary: #165DFF;
  --secondary: #36CBCB;
  --accent: #722ED1;
  --dark: #1D2129;
  --light: #F7F8FA;
  --primary-light: #E8F3FF;
  --primary-dark: #0D47A1;
  --neon-blue: #00BFFF;
  --neon-purple: #9D4EDD;
  --neon-green: #00FF9D;
  --dark-blue: #0B0E2F;
}

/* 基础样式 */
body {
  font-family: 'Inter', system-ui, sans-serif;
  background-color: var(--dark-blue);
  color: white;
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}

/* 工具类 */
.content-auto {
  content-visibility: auto;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-4px);
}

.section-padding {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 6rem 0;
  }
}

.bg-gradient-primary {
  background: linear-gradient(to right, var(--primary), var(--primary-dark));
}

/* 动画效果 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.neon-glow {
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
}

.neon-text {
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
}

.data-pulse {
  animation: pulse 2s infinite;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

/* 背景网格 */
.bg-grid {
  background-image:
    linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 裁剪路径 */
.clip-path-slant {
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

/* 导航栏样式 */
.navbar {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 50;
  transition: all 0.3s ease;
  background-color: rgba(11, 14, 47, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(22, 93, 255, 0.2);
}

.navbar.scrolled {
  background-color: rgba(11, 14, 47, 0.95);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .navbar-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .navbar-container {
    padding: 0 2rem;
  }
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

@media (min-width: 768px) {
  .navbar-content {
    height: 5rem;
  }
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-logo {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--primary);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
}

.navbar-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
}

.navbar-title .highlight {
  color: var(--neon-blue);
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
}

/* 桌面导航 */
.navbar-nav {
  display: none;
  gap: 2rem;
}

@media (min-width: 768px) {
  .navbar-nav {
    display: flex;
  }
}

.navbar-nav a {
  color: white;
  font-weight: 500;
  transition: color 0.3s ease;
  text-decoration: none;
}

.navbar-nav a:hover {
  color: var(--neon-blue);
}

/* 移动端菜单 */
.mobile-menu-toggle {
  display: block;
  color: white;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.3s ease;
}

.mobile-menu-toggle:hover {
  color: var(--neon-blue);
}

@media (min-width: 768px) {
  .mobile-menu-toggle {
    display: none;
  }
}

.mobile-menu {
  display: none;
  background-color: rgba(11, 14, 47, 0.95);
  backdrop-filter: blur(12px);
  border-top: 1px solid rgba(22, 93, 255, 0.2);
}

.mobile-menu.show {
  display: block;
}

@media (min-width: 768px) {
  .mobile-menu {
    display: none !important;
  }
}

.mobile-menu-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.75rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobile-menu a {
  display: block;
  color: white;
  font-weight: 500;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
  text-decoration: none;
}

.mobile-menu a:hover {
  color: var(--neon-blue);
}

/* 英雄区域 */
.hero-section {
  padding-top: 6rem;
  padding-bottom: 4rem;
  background: linear-gradient(to bottom, rgba(22, 93, 255, 0.05), var(--dark-blue));
  position: relative;
  overflow: hidden;
}

@media (min-width: 768px) {
  .hero-section {
    padding-top: 8rem;
    padding-bottom: 6rem;
  }
}

.hero-bg-decoration {
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  filter: blur(3rem);
}

.hero-bg-decoration.left {
  top: -10rem;
  left: -10rem;
  background-color: rgba(0, 191, 255, 0.2);
}

.hero-bg-decoration.right {
  bottom: -10rem;
  right: -10rem;
  background-color: rgba(157, 78, 221, 0.2);
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .hero-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .hero-container {
    padding: 0 2rem;
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-content {
    flex-direction: row;
  }
}

.hero-text {
  width: 100%;
  margin-bottom: 2.5rem;
}

@media (min-width: 1024px) {
  .hero-text {
    width: 50%;
    margin-bottom: 0;
  }
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: bold;
  line-height: 1.2;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-title .highlight {
  color: var(--neon-blue);
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
}

.hero-description {
  font-size: 1.125rem;
  color: #D1D5DB;
  margin-bottom: 2rem;
  max-width: 36rem;
}

@media (min-width: 768px) {
  .hero-description {
    font-size: 1.25rem;
  }
}

/* 按钮样式 */
.btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary), var(--neon-blue));
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-primary:hover {
  box-shadow: 0 0 30px rgba(0, 191, 255, 0.3);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 1px solid rgba(0, 191, 255, 0.5);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.btn i {
  margin-left: 0.5rem;
}

/* 数据指标 */
.metrics-grid {
  margin-top: 3rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.metric-card {
  background-color: rgba(29, 33, 41, 0.3);
  backdrop-filter: blur(4px);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(22, 93, 255, 0.2);
}

.metric-value {
  font-family: 'JetBrains Mono', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.metric-value.blue {
  color: var(--neon-blue);
}

.metric-value.green {
  color: var(--neon-green);
}

.metric-value.purple {
  color: var(--neon-purple);
}

.metric-label {
  font-size: 0.875rem;
  color: #9CA3AF;
}

/* 英雄图片区域 */
.hero-image {
  width: 100%;
  position: relative;
}

@media (min-width: 1024px) {
  .hero-image {
    width: 50%;
  }
}

.hero-image-container {
  position: relative;
  z-index: 10;
  animation: float 6s ease-in-out infinite;
}

.hero-image-card {
  background-color: rgba(29, 33, 41, 0.5);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(22, 93, 255, 0.3);
  overflow: hidden;
}

.hero-image-card img {
  width: 100%;
  height: auto;
}

.hero-image-info {
  padding: 1rem;
  border-top: 1px solid rgba(22, 93, 255, 0.2);
}

.hero-image-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-icon {
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(0, 255, 157, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neon-green);
}

.status-text h4 {
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  margin-bottom: 0.25rem;
}

.status-text p {
  font-size: 0.75rem;
  color: #9CA3AF;
}

.status-indicator {
  font-size: 0.75rem;
  color: #9CA3AF;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-dot {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--neon-green);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 数据仪表盘 */
.dashboard-section {
  padding: 3rem 0;
  background-color: rgba(29, 33, 41, 0.5);
  position: relative;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .dashboard-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .dashboard-container {
    padding: 0 2rem;
  }
}

.dashboard-card {
  background-color: rgba(29, 33, 41, 0.5);
  backdrop-filter: blur(12px);
  border-radius: 0.75rem;
  border: 1px solid rgba(22, 93, 255, 0.2);
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.dashboard-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .dashboard-header {
    flex-direction: row;
    align-items: center;
    margin-bottom: 1.5rem;
  }
}

.dashboard-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .dashboard-title {
    margin-bottom: 0;
  }
}

.dashboard-title .subtitle {
  color: var(--neon-blue);
  font-size: 0.875rem;
  font-weight: normal;
}

.dashboard-tabs {
  display: flex;
  gap: 0.75rem;
}

.dashboard-tab {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
}

.dashboard-tab.active {
  background-color: rgba(22, 93, 255, 0.2);
  color: white;
}

.dashboard-tab:not(.active) {
  background-color: rgba(22, 93, 255, 0.1);
  color: #9CA3AF;
}

.dashboard-tab:hover {
  background-color: rgba(22, 93, 255, 0.3);
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .charts-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.chart-card {
  background-color: rgba(29, 33, 41, 0.3);
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid rgba(22, 93, 255, 0.2);
}

.chart-title {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.chart-container {
  height: 12rem;
}

/* 功能卡片区域 */
.features-section {
  padding: 4rem 0;
  background-color: var(--dark-blue);
  position: relative;
  overflow: hidden;
}

@media (min-width: 768px) {
  .features-section {
    padding: 6rem 0;
  }
}

.features-bg-decoration {
  position: absolute;
  width: 15rem;
  height: 15rem;
  border-radius: 50%;
  filter: blur(3rem);
}

.features-bg-decoration.left {
  top: 25%;
  left: -5rem;
  background-color: rgba(157, 78, 221, 0.1);
}

.features-bg-decoration.right {
  bottom: 25%;
  right: -5rem;
  background-color: rgba(0, 191, 255, 0.1);
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .features-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .features-container {
    padding: 0 2rem;
  }
}

.features-header {
  text-align: center;
  max-width: 48rem;
  margin: 0 auto 4rem auto;
}

.features-title {
  font-size: clamp(1.8rem, 4vw, 2.8rem);
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.features-description {
  font-size: 1.125rem;
  color: #D1D5DB;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.feature-card {
  background-color: rgba(29, 33, 41, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(22, 93, 255, 0.2);
  transition: all 0.3s ease;
  group: true;
}

.feature-card:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-4px);
}

.feature-icon {
  width: 3.5rem;
  height: 3.5rem;
  background-color: rgba(22, 93, 255, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.25rem;
  transition: background-color 0.3s ease;
}

.feature-card:hover .feature-icon {
  background-color: rgba(22, 93, 255, 0.3);
}

.feature-icon i {
  color: var(--neon-blue);
  font-size: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.75rem;
  color: white;
}

.feature-text {
  color: #9CA3AF;
  line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .metrics-grid {
    gap: 0.75rem;
  }

  .metric-card {
    padding: 0.75rem;
  }

  .metric-value {
    font-size: 1.25rem;
  }

  .dashboard-section {
    padding: 2rem 0;
  }

  .features-section {
    padding: 3rem 0;
  }

  .features-header {
    margin-bottom: 3rem;
  }
}

/* 系统优势区域 */
.advantages-section {
  padding: 4rem 0;
  background: linear-gradient(to bottom, var(--dark-blue), var(--dark));
  position: relative;
  overflow: hidden;
  clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
}

@media (min-width: 768px) {
  .advantages-section {
    padding: 6rem 0;
  }
}

.advantages-bg-decoration {
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  filter: blur(3rem);
}

.advantages-bg-decoration.left {
  top: 33%;
  left: -10rem;
  background-color: rgba(0, 191, 255, 0.1);
}

.advantages-bg-decoration.right {
  bottom: 33%;
  right: -10rem;
  background-color: rgba(157, 78, 221, 0.1);
}

.advantages-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .advantages-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .advantages-container {
    padding: 0 2rem;
  }
}

.advantages-header {
  text-align: center;
  max-width: 48rem;
  margin: 0 auto 4rem auto;
}

.advantages-title {
  font-size: clamp(1.8rem, 4vw, 2.8rem);
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.advantages-description {
  font-size: 1.125rem;
  color: #D1D5DB;
}

.advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.advantage-card {
  background-color: rgba(29, 33, 41, 0.5);
  backdrop-filter: blur(4px);
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 191, 255, 0.3);
  transition: all 0.3s ease;
}

.advantage-card:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-4px);
}

.advantage-icon {
  width: 3rem;
  height: 3rem;
  background-color: rgba(0, 191, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neon-blue);
  margin-bottom: 1rem;
  transition: background-color 0.3s ease;
}

.advantage-card:hover .advantage-icon {
  background-color: rgba(0, 191, 255, 0.3);
}

.advantage-icon i {
  font-size: 1.25rem;
}

.advantage-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.75rem;
  color: white;
}

.advantage-text {
  color: #9CA3AF;
  line-height: 1.6;
}

/* 管理流程区域 */
.process-section {
  padding: 4rem 0;
  background-color: var(--dark);
  position: relative;
  overflow: hidden;
}

@media (min-width: 768px) {
  .process-section {
    padding: 6rem 0;
  }
}

.process-bg-decoration {
  position: absolute;
  width: 16rem;
  height: 16rem;
  border-radius: 50%;
  filter: blur(3rem);
}

.process-bg-decoration.left {
  top: 25%;
  left: -8rem;
  background-color: rgba(157, 78, 221, 0.1);
}

.process-bg-decoration.right {
  bottom: 25%;
  right: -8rem;
  background-color: rgba(0, 191, 255, 0.1);
}

.process-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .process-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .process-container {
    padding: 0 2rem;
  }
}

.process-header {
  text-align: center;
  max-width: 48rem;
  margin: 0 auto 4rem auto;
}

.process-title {
  font-size: clamp(1.8rem, 4vw, 2.8rem);
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.process-description {
  font-size: 1.125rem;
  color: #D1D5DB;
}

.process-timeline {
  position: relative;
}

.process-line {
  display: none;
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 0.25rem;
  background: linear-gradient(to bottom, var(--neon-blue), var(--neon-purple));
  transform: translateX(-50%);
}

@media (min-width: 768px) {
  .process-line {
    display: block;
  }
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  position: relative;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

@media (min-width: 768px) {
  .process-step {
    flex-direction: row;
  }
}

.process-step-content {
  width: 100%;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .process-step-content {
    width: 50%;
    margin-bottom: 0;
  }

  .process-step:nth-child(even) .process-step-content {
    order: 3;
    padding-left: 3rem;
  }

  .process-step:nth-child(odd) .process-step-content {
    padding-right: 3rem;
    text-align: right;
  }
}

.process-step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin: 0 1rem;
}

@media (min-width: 768px) {
  .process-step-number {
    order: 2;
    margin: 0;
  }
}

.process-step:nth-child(1) .process-step-number {
  background-color: var(--neon-blue);
}

.process-step:nth-child(2) .process-step-number {
  background-color: var(--neon-purple);
}

.process-step:nth-child(3) .process-step-number {
  background-color: var(--neon-green);
}

.process-step:nth-child(4) .process-step-number {
  background-color: var(--neon-blue);
}

.process-step:nth-child(5) .process-step-number {
  background-color: var(--neon-purple);
}

.process-step-image {
  width: 100%;
  margin-top: 2rem;
}

@media (min-width: 768px) {
  .process-step-image {
    width: 50%;
    margin-top: 0;
  }

  .process-step:nth-child(even) .process-step-image {
    order: 1;
    padding-right: 3rem;
  }

  .process-step:nth-child(odd) .process-step-image {
    padding-left: 3rem;
  }
}

.process-step-image img {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.process-step-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.75rem;
}

.process-step-text {
  color: #9CA3AF;
  line-height: 1.6;
}

/* 联系我们区域 */
.contact-section {
  padding: 4rem 0;
  background-color: var(--dark-blue);
  position: relative;
  overflow: hidden;
}

@media (min-width: 768px) {
  .contact-section {
    padding: 6rem 0;
  }
}

.contact-bg-decoration {
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  filter: blur(3rem);
}

.contact-bg-decoration.top-right {
  top: -10rem;
  right: -10rem;
  background-color: rgba(157, 78, 221, 0.1);
}

.contact-bg-decoration.bottom-left {
  bottom: -10rem;
  left: -10rem;
  background-color: rgba(0, 191, 255, 0.1);
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .contact-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .contact-container {
    padding: 0 2rem;
  }
}

.contact-wrapper {
  max-width: 80rem;
  margin: 0 auto;
}

.contact-card {
  background-color: rgba(29, 33, 41, 0.5);
  backdrop-filter: blur(4px);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(22, 93, 255, 0.3);
  overflow: hidden;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-form-section {
  padding: 2rem;
}

@media (min-width: 768px) {
  .contact-form-section {
    padding: 3rem;
  }
}

.contact-info-section {
  padding: 2rem;
  background-color: rgba(29, 33, 41, 0.3);
  backdrop-filter: blur(4px);
}

@media (min-width: 768px) {
  .contact-info-section {
    padding: 3rem;
  }
}

.contact-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
}

.contact-description {
  color: #9CA3AF;
  margin-bottom: 2rem;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #9CA3AF;
  margin-bottom: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--dark);
  border: 1px solid rgba(22, 93, 255, 0.3);
  border-radius: 0.5rem;
  color: white;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--neon-blue);
  box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.2);
}

.form-input::placeholder {
  color: #6B7280;
}

.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

.form-submit {
  width: 100%;
  background: linear-gradient(to right, var(--primary), var(--neon-blue));
  color: white;
  font-weight: 500;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.form-submit:hover {
  box-shadow: 0 0 30px rgba(0, 191, 255, 0.3);
}

.form-submit:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.contact-info-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1.5rem;
}

.contact-info-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-info-item {
  display: flex;
  align-items: flex-start;
}

.contact-info-icon {
  width: 3rem;
  height: 3rem;
  background-color: rgba(0, 191, 255, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neon-blue);
  margin-right: 1rem;
  flex-shrink: 0;
}

.contact-info-icon i {
  font-size: 1.125rem;
}

.contact-info-content h4 {
  color: white;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.contact-info-content p {
  color: #9CA3AF;
}

.social-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(22, 93, 255, 0.2);
}

.social-title {
  color: white;
  font-weight: 500;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(0, 191, 255, 0.2);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neon-blue);
  text-decoration: none;
  transition: background-color 0.3s ease;
}

.social-link:hover {
  background-color: rgba(0, 191, 255, 0.3);
}

/* 页脚区域 */
.footer {
  background-color: var(--dark);
  border-top: 1px solid rgba(22, 93, 255, 0.2);
  padding: 3rem 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .footer-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .footer-container {
    padding: 0 2rem;
  }
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer-company {
  grid-column: span 1;
}

@media (min-width: 768px) {
  .footer-company {
    grid-column: span 2;
  }
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.footer-logo {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--primary);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
}

.footer-brand-text {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
}

.footer-brand-text .highlight {
  color: var(--neon-blue);
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
}

.footer-description {
  color: #9CA3AF;
  margin-bottom: 1rem;
  max-width: 28rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  color: #9CA3AF;
  font-size: 1.25rem;
  transition: color 0.3s ease;
  text-decoration: none;
}

.footer-social a:hover {
  color: var(--neon-blue);
}

.footer-section h3 {
  color: white;
  font-weight: bold;
  margin-bottom: 1rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-links a {
  color: #9CA3AF;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--neon-blue);
}

.footer-contact-item {
  color: #9CA3AF;
  margin-bottom: 0.5rem;
}

.footer-contact-item i {
  color: var(--neon-blue);
  margin-right: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid rgba(22, 93, 255, 0.2);
  margin-top: 2rem;
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom p {
  color: #9CA3AF;
}

.footer-bottom a {
  color: var(--neon-blue);
  text-decoration: none;
}

.footer-bottom a:hover {
  text-decoration: underline;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 3rem;
  height: 3rem;
  background-color: var(--primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  border: none;
  cursor: pointer;
  z-index: 50;
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
}

.back-to-top:hover {
  background-color: var(--primary-dark);
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}
