/**
 * 智慧食堂平台 JavaScript
 */

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeScrollEffects();
    initializeCharts();
    initializeContactForm();
    initializeAnimations();
});

/**
 * 初始化导航功能
 */
function initializeNavigation() {
    // 移动端菜单切换
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            mobileMenu.classList.toggle('show');
        });
    }

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 关闭移动端菜单
                if (mobileMenu) {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.classList.remove('show');
                }
            }
        });
    });
}

/**
 * 初始化滚动效果
 */
function initializeScrollEffects() {
    const navbar = document.getElementById('navbar');
    const backToTop = document.getElementById('back-to-top');
    
    // 导航栏滚动效果
    window.addEventListener('scroll', function() {
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
                navbar.style.backgroundColor = 'rgba(11, 14, 47, 0.95)';
            } else {
                navbar.classList.remove('scrolled');
                navbar.style.backgroundColor = 'rgba(11, 14, 47, 0.8)';
            }
        }
        
        // 返回顶部按钮
        if (backToTop) {
            if (window.scrollY > 100) {
                backToTop.style.opacity = '1';
                backToTop.style.visibility = 'visible';
            } else {
                backToTop.style.opacity = '0';
                backToTop.style.visibility = 'hidden';
            }
        }
    });

    // 返回顶部功能
    if (backToTop) {
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

/**
 * 初始化图表
 */
function initializeCharts() {
    // 检查 Chart.js 是否加载
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js 未加载，跳过图表初始化');
        return;
    }

    // 效率图表
    const efficiencyCtx = document.getElementById('efficiencyChart');
    if (efficiencyCtx) {
        new Chart(efficiencyCtx, {
            type: 'line',
            data: {
                labels: ['6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00'],
                datasets: [{
                    label: '运营效率',
                    data: [65, 78, 85, 92, 88, 85, 90],
                    borderColor: '#00BFFF',
                    backgroundColor: 'rgba(0, 191, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(22, 93, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(22, 93, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    }
                }
            }
        });
    }

    // 安全检测图表
    const safetyCtx = document.getElementById('safetyChart');
    if (safetyCtx) {
        new Chart(safetyCtx, {
            type: 'doughnut',
            data: {
                labels: ['合格', '待检', '不合格'],
                datasets: [{
                    data: [85, 12, 3],
                    backgroundColor: ['#00FF9D', '#00BFFF', '#FF6B6B'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#9CA3AF',
                            usePointStyle: true,
                            padding: 15
                        }
                    }
                }
            }
        });
    }

    // 满意度图表
    const satisfactionCtx = document.getElementById('satisfactionChart');
    if (satisfactionCtx) {
        new Chart(satisfactionCtx, {
            type: 'bar',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五'],
                datasets: [{
                    label: '满意度',
                    data: [4.2, 4.5, 4.3, 4.7, 4.6],
                    backgroundColor: 'rgba(157, 78, 221, 0.8)',
                    borderColor: '#9D4EDD',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5,
                        grid: {
                            color: 'rgba(22, 93, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(22, 93, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9CA3AF'
                        }
                    }
                }
            }
        });
    }
}

/**
 * 初始化联系表单
 */
function initializeContactForm() {
    const contactForm = document.querySelector('#contact form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(this);
            const name = formData.get('name');
            const email = formData.get('email');
            const phone = formData.get('phone');
            const message = formData.get('message');
            
            // 简单验证
            if (!name || !email || !message) {
                showAlert('请填写必填字段', 'error');
                return;
            }
            
            // 邮箱格式验证
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showAlert('请输入有效的邮箱地址', 'error');
                return;
            }
            
            // 模拟提交
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>发送中...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                showAlert('消息发送成功！我们会尽快与您联系。', 'success');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }
}

/**
 * 初始化动画效果
 */
function initializeAnimations() {
    // 数据动画效果
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('data-pulse');
                
                // 数字动画
                if (entry.target.classList.contains('metric-value')) {
                    animateNumber(entry.target);
                }
            }
        });
    }, observerOptions);

    // 观察数据指标元素
    document.querySelectorAll('.metric-value, .font-code').forEach(el => {
        observer.observe(el);
    });
}

/**
 * 数字动画效果
 */
function animateNumber(element) {
    const text = element.textContent;
    const number = parseFloat(text.replace(/[^\d.]/g, ''));
    const suffix = text.replace(/[\d.]/g, '');
    
    if (isNaN(number)) return;
    
    let current = 0;
    const increment = number / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= number) {
            current = number;
            clearInterval(timer);
        }
        element.textContent = current.toFixed(1) + suffix;
    }, 30);
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建提示元素
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        max-width: 300px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // 设置背景色
    switch(type) {
        case 'success':
            alert.style.backgroundColor = '#10B981';
            break;
        case 'error':
            alert.style.backgroundColor = '#EF4444';
            break;
        default:
            alert.style.backgroundColor = '#3B82F6';
    }
    
    alert.textContent = message;
    document.body.appendChild(alert);
    
    // 显示动画
    setTimeout(() => {
        alert.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        alert.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(alert);
        }, 300);
    }, 3000);
}

/**
 * 工具函数：防抖
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 工具函数：节流
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
