{% extends 'base.html' %}

{% block title %}智慧食堂平台 - {{ super() }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/home.css') }}">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- 标题区域 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">智慧食堂平台升级</h1>
        <p class="text-gray-600 text-sm">• 内容由 AI 生成，不能完全保障真实</p>
    </div>

    <!-- 核心功能展示 -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">核心功能</h2>
        
        <!-- 食材全流程溯源 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-3">食材全流程溯源</h3>
            <div class="space-y-4">
                <div class="flex items-start">
                    <span class="text-blue-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">周菜单智能解析</p>
                        <p class="text-sm text-gray-500">自动识别菜谱所需食材，一键生成采购计划</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="text-blue-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">采购单自动采购</p>
                        <p class="text-sm text-gray-500">供应商自动匹配，采购进度实时追踪</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="text-blue-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">入库前智能验收</p>
                        <p class="text-sm text-gray-500">自动推送采购单至验收，扫码验收、信息留痕</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能管理功能 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-3">智能管理功能</h3>
            <div class="space-y-4">
                <div class="flex items-start">
                    <span class="text-green-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">凭证自动绑定</p>
                        <p class="text-sm text-gray-500">入库凭证自动归档，检验检疫/质量报告自动关联</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="text-green-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">一键式入库</p>
                        <p class="text-sm text-gray-500">验收合格后自动入库，库存自动更新</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="text-green-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">智能留样标签</p>
                        <p class="text-sm text-gray-500">自动生成留样标签，一键打印、全程追溯</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统优势 -->
        <div>
            <h3 class="text-lg font-semibold text-gray-700 mb-3">系统优势</h3>
            <div class="space-y-4">
                <div class="flex items-start">
                    <span class="text-purple-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">全流程食材溯源</p>
                        <p class="text-sm text-gray-500">采购-验收-入库-留样全链路追溯，数据可视化、责任到人</p>
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="text-purple-500 mr-2">•</span>
                    <div>
                        <p class="text-gray-700">智能数据分析</p>
                        <p class="text-sm text-gray-500">通过数据分析助力管理者做出科学决策，优化食堂运营效率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-center space-x-4">
            <a href="{{ url_for('auth.login') }}" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                立即体验
            </a>
            <a href="{{ url_for('auth.register') }}" class="bg-gray-100 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                注册账号
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 平滑滚动
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});
</script>
{% endblock %}
